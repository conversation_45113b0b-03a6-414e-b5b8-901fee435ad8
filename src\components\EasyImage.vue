<template>
  <div
    class="flex items-center easy-image-wrapper"
    :style="{
      width: `${props.w}px`,
      flex: `0 0 ${props.w}px`,
      backgroundColor: props.src ? '#fff' : '',
      boxShadow: props.src ? '0 0 1px 0 rgb(0 0 0 / 15%)' : '',
    }"
  >
    <div v-if="imgLoading || imgError" :style="{ width: `${props.w}px` }" class="flex justify-center items-center">
      <span v-if="imgError" class="text-red-500">加载失败</span>
      <a-spin v-else :indicator="indicator" />
    </div>
    <a-image
      v-if="zoomImgSrc && !imgLoading && !imgError"
      class="object-contain"
      :width="props.w"
      :height="props.h"
      :src="zoomImgSrc"
      :preview="props.hidePreview ? false : { maskClassName: props.maskClassName, src: props.src as string }"
      referrerpolicy="no-referrer"
      :style="props.imgStyle"
    >
      <template #previewMask>
        <EyeOutlined />
      </template>
    </a-image>
    <span v-if="!src" class="iconfont icon-icon-wrapper w-full inline-flex items-center text-#C0C0C0" :style="{ fontSize: props.h + 'px', height: props.h + 'px' }"></span>
  </div>
</template>

<script lang="ts" setup>
import { LoadingOutlined, EyeOutlined } from '@ant-design/icons-vue'

const props = withDefaults(
  defineProps<{
    src: string | null
    w?: number
    h?: number
    maskClassName?: string
    hidePreview?: boolean
    imgStyle?: any
  }>(),
  {
    w: 28,
    h: 28,
    maskClassName: '预览',
    src: null,
    hidePreview: false,
    imgStyle: () => {},
  },
)

const indicator = h(LoadingOutlined, {
  style: {
    fontSize: props.h - 5,
  },
  spin: true,
})

const zoomImgSrc = ref<string | null>(null)
const ossModeParams = ref(`?x-oss-process=image/resize,w_${props.w + 50}`)
const imgLoading = ref(false)
const imgError = ref(false)

const loadImg = (value, params) => {
  imgLoading.value = true
  imgError.value = false
  const img = new Image()
  img.src = value + (params?.push || '')

  // 回收资源
  const recycleImage = () => {
    img.onerror = null
    img.onload = null
    img.src = ''
  }

  img.onload = () => {
    zoomImgSrc.value = img.src
    imgLoading.value = false
    recycleImage()
  }

  img.onerror = () => {
    if (!params?.push) {
      imgError.value = true
    } else {
      loadImg(value, {})
    }
    recycleImage()
  }
}

watch(
  () => props.src,
  (value) => {
    if (!value) {
      zoomImgSrc.value = null
      return
    }
    loadImg(value, {
      push: ossModeParams.value,
    })
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.easy-image {
  &-wrapper {
    overflow: hidden;
    border-radius: 4px;
  }
}
</style>
