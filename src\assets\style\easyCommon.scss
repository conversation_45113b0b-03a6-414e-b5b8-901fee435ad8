.easy-button-dot {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  height: 16px;
  padding: 4px 6px;
  font-size: 10px;
  line-height: 1;
  color: #fff;
  text-decoration: none;
  text-shadow: none;
  text-transform: uppercase;
  background-color: #ff4d4f;
  border-radius: 16px;
  transform: translate(50%, -50%) scale(0.9);
  transform-origin: center bottom;

  &.inline-dot {
    position: relative;
    display: inline-flex;
    transform: scale(0.965);
    transform-origin: center center;
  }
}

.easy-tag {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 24px;
  padding: 0 10px;
  color: #666;
  white-space: nowrap;
  cursor: pointer;
  background-color: #f0f0f0;
  border-radius: 4px;

  &.no-border {
    height: 20px;
    min-height: 20px !important;
    padding: 0 4px;
    border: none !important;
    border-radius: 2px;
  }

  &.default {
    color: #0f1521;
    background-color: #edf2fa;
    border-color: #0f1521;
  }

  &.tip {
    color: #5530fc;
    background-color: #ece8ff;
    border-color: #5530fc;
  }

  &--0,
  &--1,
  &.info {
    color: #2a82e4;
    background-color: #e8f0ff;
    border-color: #2a82e4;
  }

  // 待N级审核
  &--20,
  &--30,
  &--40,
  &--50,
  &.audit {
    color: #ac33c1;
    background-color: #f9e9fb;
    border-color: #ac33c1;
  }

  &.disable {
    color: #00baad;
    background-color: transparent;
    border-color: transparent;
  }

  &--3,
  &--10,
  &.wait {
    color: #ff8d1a;
    background-color: #fff9f1;
    border-color: #ff8d1a;
  }

  &--4,
  &.warn {
    color: #ff5733;
    background-color: #fff0ed;
    border-color: #fff0ed;
  }

  &--2,
  &--90,
  &--100,
  &.success {
    color: #8dbd29;
    background-color: #f6fff2;
    border-color: #8dbd29;
  }

  &--95,
  &.error {
    color: #eb1237;
    background-color: #fff0ed;
    border-color: #eb1237;
  }

  &.current {
    color: #fff;
    background-color: #1890ff;
  }

  &.cursor-default {
    cursor: default;

    &:hover {
      opacity: 1 !important;
    }
  }

  &:hover {
    opacity: 0.8;
  }

  & + .easy-tag {
    margin-left: 10px;
  }
}

// 文字样式 eg: ● 等待中
.easy-label-status {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding-left: 10px;
  font-size: 12px;
  line-height: 20px;

  &::before {
    position: absolute;
    left: 0;
    display: inline-block;
    font-family: Arial, sans-serif !important;
    font-size: 12px;
    line-height: 0;
    vertical-align: top;
    content: '●';
  }

  &.success {
    color: #00b42a;
  }

  &.error {
    color: #ff4d4f;
  }

  &.wait,
  &.warn,
  &.audit {
    color: #ff8d1a;
  }

  &.info {
    color: #1890ff;
  }

  &.disabled {
    color: #999;
  }

  &.default {
    color: #1890ff;
  }
}

.easy-ripple-button {
  position: relative;
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
  color: #333;
  border: 1px solid #dcdee0;
  border-radius: 8px;
}

// 单选的btn-tab样式
.easy-tabs-single {
  display: flex;
  overflow: hidden;
  border-radius: 4px;

  &-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 26px;
    padding: 0 8px;
    color: #666;
    border: 1px #dcdee0 solid;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &.big {
      height: 28px;
      padding: 0 24px;
    }

    & + .easy-tabs-single-btn {
      margin-left: -1px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;

      &::after {
        display: none !important;
      }
    }

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      display: none;
      width: 1px;
      content: '';
      background: $color;
    }

    &:hover {
      cursor: pointer;
      border-color: $color;

      &::after {
        display: block;
      }
    }

    &.current {
      color: #fff;
      background-color: $color;
      border-color: $color;

      .count {
        color: #fff;
      }
    }

    .count {
      margin-left: 4px;
      color: #ff4d4f;

      &::before {
        content: '+';
      }
    }
  }
}

// 多选打勾btn样式
.easy-checkbox-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  height: 26px;
  padding: 3px 8px;
  margin-right: 4px;
  overflow: hidden;
  font-size: 12px;
  color: #666;
  background: #fff;
  border: 1px solid #dcdee0;
  border-radius: 4px;
  transition: 0.3s all;

  &:hover {
    color: $color;
    cursor: pointer;
    border-color: $color;
  }

  // & + .easy-checkbox-btn {
  //   margin-left: 4px;
  // }

  .count {
    margin-left: 2px;
    color: #ff4d4f;

    &::before {
      content: '+';
    }
  }

  &.active {
    color: $color;
    background: #fff;
    border: 1px solid $color;

    &::before {
      position: absolute;
      top: -2px;
      right: -7px;
      display: block;
      width: 0;
      height: 0;
      content: '';
      border-right: 10px solid transparent;
      border-bottom: 10px solid $color;
      border-left: 10px solid transparent;
      transition: all 0.3s;
      transform: rotate(45deg);
    }

    &::after {
      position: absolute;
      top: -2px;
      right: 0;
      box-sizing: border-box;
      display: table;
      width: 4px;
      height: 9px;
      content: '';
      border: 1px solid #fff;
      border-inline-start: 0;
      border-top: 0;
      transform: rotate(45deg) scale(1);
    }
  }
}
